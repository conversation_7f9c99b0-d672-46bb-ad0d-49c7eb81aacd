{"name": "gcandle-saas-lambda", "version": "1.0.0", "description": "Lambda functions for Gcandle SaaS subscription management", "main": "index.js", "scripts": {"build": "npm run clean && npm run compile", "clean": "rm -rf dist", "compile": "tsc", "deploy": "npm install && npm run build && rm -rf node_modules && npm install --omit=dev && zip -r lambda-functions.zip dist/ node_modules/ && npm install", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "stripe": "^18.3.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.134", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.11.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "jest": "^29.7.0", "serverless-offline": "^14.4.0", "typescript": "^5.3.3"}, "keywords": ["aws", "lambda", "stripe", "saas", "subscription", "billing"], "author": "Gcandle Team", "license": "MIT"}