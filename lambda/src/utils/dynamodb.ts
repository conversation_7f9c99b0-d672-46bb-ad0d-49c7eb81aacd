/**
 * DynamoDB utilities for Lambda functions
 */

import * as AWS from 'aws-sdk';
import { getConfigInstance } from './config';
import { User, Subscription, Usage } from '../types';

// Initialize DynamoDB client
const config = getConfigInstance();
const docClient = new AWS.DynamoDB.DocumentClient({ region: config.aws.region });

/**
 * User operations
 */
export class UserRepository {
  private tableName = config.aws.dynamodb.usersTable;

  async getUser(userId: string): Promise<User | null> {
    try {
      const result = await docClient.get({
        TableName: this.tableName,
        Key: { userId },
      }).promise();
      
      return result.Item as User || null;
    } catch (error) {
      console.error('Error getting user:', error);
      throw new Error('Failed to get user');
    }
  }

  async createUser(user: Omit<User, 'createdAt' | 'updatedAt'>): Promise<User> {
    const now = new Date().toISOString();
    const newUser: User = {
      ...user,
      createdAt: now,
      updatedAt: now,
    };

    try {
      await docClient.put({
        TableName: this.tableName,
        Item: newUser,
        ConditionExpression: 'attribute_not_exists(userId)',
      }).promise();
      
      return newUser;
    } catch (error) {
      if ((error as any).code === 'ConditionalCheckFailedException') {
        throw new Error('User already exists');
      }
      console.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  async getUserByStripeCustomerId(stripeCustomerId: string): Promise<User | null> {
    try {
      const result = await docClient.scan({
        TableName: this.tableName,
        FilterExpression: 'stripeCustomerId = :stripeCustomerId',
        ExpressionAttributeValues: {
          ':stripeCustomerId': stripeCustomerId,
        },
      }).promise();
      
      return result.Items?.[0] as User || null;
    } catch (error) {
      console.error('Error getting user by Stripe customer ID:', error);
      throw new Error('Failed to get user by Stripe customer ID');
    }
  }
}

/**
 * Subscription operations
 */
export class SubscriptionRepository {
  private tableName = config.aws.dynamodb.subscriptionsTable;

  async getUserSubscriptions(userId: string): Promise<Subscription[]> {
    try {
      const result = await docClient.query({
        TableName: this.tableName,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
      }).promise();
      
      return result.Items as Subscription[] || [];
    } catch (error) {
      console.error('Error getting user subscriptions:', error);
      throw new Error('Failed to get user subscriptions');
    }
  }

  async getActiveSubscription(userId: string): Promise<Subscription | null> {
    const subscriptions = await this.getUserSubscriptions(userId);
    return subscriptions.find(sub => sub.status === 'active' || sub.status === 'trialing') || null;
  }

  async createSubscription(subscription: Omit<Subscription, 'createdAt' | 'updatedAt'>): Promise<Subscription> {
    const now = new Date().toISOString();
    const newSubscription: Subscription = {
      ...subscription,
      createdAt: now,
      updatedAt: now,
    };

    try {
      await docClient.put({
        TableName: this.tableName,
        Item: newSubscription,
      }).promise();
      
      return newSubscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error('Failed to create subscription');
    }
  }

  async getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | null> {
    try {
      const result = await docClient.scan({
        TableName: this.tableName,
        FilterExpression: 'stripeSubscriptionId = :stripeSubscriptionId',
        ExpressionAttributeValues: {
          ':stripeSubscriptionId': stripeSubscriptionId,
        },
      }).promise();
      
      return result.Items?.[0] as Subscription || null;
    } catch (error) {
      console.error('Error getting subscription by Stripe ID:', error);
      throw new Error('Failed to get subscription by Stripe ID');
    }
  }
}

/**
 * Usage operations
 */
export class UsageRepository {
  private tableName = config.aws.dynamodb.usageTable;

  async updateUsage(userId: string, date: string, updates: Partial<Usage>): Promise<Usage> {
    const now = new Date().toISOString();
    
    try {
      const result = await docClient.update({
        TableName: this.tableName,
        Key: { userId, date },
        UpdateExpression: 'SET searches = if_not_exists(searches, :zero) + :searchIncrement, apiCalls = if_not_exists(apiCalls, :zero) + :apiIncrement, lastUpdated = :lastUpdated',
        ExpressionAttributeValues: {
          ':zero': 0,
          ':searchIncrement': updates.searches || 0,
          ':apiIncrement': updates.apiCalls || 0,
          ':lastUpdated': now,
        },
        ReturnValues: 'ALL_NEW',
      }).promise();
      
      return result.Attributes as Usage;
    } catch (error) {
      console.error('Error updating usage:', error);
      throw new Error('Failed to update usage');
    }
  }
}

// Export repository instances
export const userRepository = new UserRepository();
export const subscriptionRepository = new SubscriptionRepository();
export const usageRepository = new UsageRepository();
