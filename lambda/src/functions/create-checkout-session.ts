/**
 * <PERSON><PERSON> function to create Stripe checkout session
 * Endpoint: POST /stripe/create-checkout-session
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { 
  getAuthenticatedUser, 
  validateRequestBody, 
  successResponse, 
  errorResponse, 
  handleCORS 
} from '../utils/auth';
import { userRepository } from '../utils/dynamodb';
import { createCheckoutSession, createStripeCustomer } from '../utils/stripe';
import { CreateCheckoutSessionRequest } from '../types';

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log('Create checkout session request:', JSON.stringify(event, null, 2));

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return handleCORS();
  }

  try {
    // Authenticate user
    const cognitoUser = await getAuthenticatedUser(event);
    
    // Validate request body
    const { priceId, successUrl, cancelUrl } = validateRequestBody<CreateCheckoutSessionRequest>(
      event,
      ['priceId']
    );

    // Get or create user in our database
    let user = await userRepository.getUser(cognitoUser.sub);
    
    if (!user) {
      // Create new user if doesn't exist
      user = await userRepository.createUser({
        userId: cognitoUser.sub,
        email: cognitoUser.email,
        name: cognitoUser.name,
        plan: 'free',
        emailVerified: cognitoUser.email_verified,
      });
    }

    // Get or create Stripe customer
    let stripeCustomerId = user.stripeCustomerId;
    
    if (!stripeCustomerId) {
      // Create Stripe customer
      const stripeCustomer = await createStripeCustomer(
        user.email,
        user.name,
        {
          userId: user.userId,
          email: user.email,
        }
      );
      
      stripeCustomerId = stripeCustomer.id;
      
      // Update user with Stripe customer ID
      await userRepository.updateUser(user.userId, {
        stripeCustomerId,
      });
    }

    // Create checkout session
    const session = await createCheckoutSession(
      priceId,
      stripeCustomerId,
      successUrl,
      cancelUrl,
      {
        userId: user.userId,
        email: user.email,
        priceId,
      }
    );

    return successResponse({
      sessionId: session.id,
      url: session.url,
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No authorization token')) {
        return errorResponse('Authentication required', 401);
      }
      if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
        return errorResponse('Invalid or expired token', 401);
      }
      if (error.message.includes('Missing required field')) {
        return errorResponse(error.message, 400);
      }
      
      return errorResponse(error.message, 500);
    }
    
    return errorResponse('Internal server error', 500);
  }
};

// Export for testing
export { CreateCheckoutSessionRequest };
