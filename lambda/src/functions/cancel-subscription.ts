/**
 * Lambda function to cancel user's subscription
 * Endpoint: POST /stripe/subscription/{subscriptionId}/cancel
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { 
  getAuthenticatedUser, 
  successResponse, 
  errorResponse, 
  handleCORS 
} from '../utils/auth';
import { userRepository, subscriptionRepository } from '../utils/dynamodb';
import { cancelStripeSubscription } from '../utils/stripe';

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log('Cancel subscription request:', JSON.stringify(event, null, 2));

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return handleCORS();
  }

  try {
    // Authenticate user
    const cognitoUser = await getAuthenticatedUser(event);
    
    // Get subscription ID from path parameters
    const subscriptionId = event.pathParameters?.subscriptionId;
    
    if (!subscriptionId) {
      return errorResponse('Subscription ID is required', 400);
    }

    // Get user from database
    const user = await userRepository.getUser(cognitoUser.sub);
    
    if (!user) {
      return errorResponse('User not found', 404);
    }

    // Get subscription from database
    const subscription = await subscriptionRepository.getSubscription(user.userId, subscriptionId);
    
    if (!subscription) {
      return errorResponse('Subscription not found', 404);
    }

    // Verify subscription belongs to user
    if (subscription.userId !== user.userId) {
      return errorResponse('Unauthorized to cancel this subscription', 403);
    }

    // Check if subscription is already canceled
    if (subscription.status === 'canceled') {
      return errorResponse('Subscription is already canceled', 400);
    }

    // Parse request body for cancellation options
    let cancelAtPeriodEnd = true; // Default to cancel at period end
    
    if (event.body) {
      try {
        const body = JSON.parse(event.body);
        if (typeof body.cancelAtPeriodEnd === 'boolean') {
          cancelAtPeriodEnd = body.cancelAtPeriodEnd;
        }
      } catch (parseError) {
        // Ignore parse errors, use default
      }
    }

    // Cancel subscription in Stripe
    const stripeSubscription = await cancelStripeSubscription(
      subscription.stripeSubscriptionId,
      cancelAtPeriodEnd
    );

    // Update subscription in database
    const updatedSubscription = await subscriptionRepository.updateSubscription(
      subscription.userId,
      subscription.subscriptionId,
      {
        status: stripeSubscription.status as any,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end || false,
        currentPeriodEnd: (stripeSubscription as any).current_period_end,
      }
    );

    // If immediate cancellation, update user plan to free
    if (!cancelAtPeriodEnd || stripeSubscription.status === 'canceled') {
      await userRepository.updateUser(user.userId, {
        plan: 'free',
      });
    }

    return successResponse({
      subscription: updatedSubscription,
      message: cancelAtPeriodEnd 
        ? 'Subscription will be canceled at the end of the current billing period'
        : 'Subscription has been canceled immediately',
    });

  } catch (error) {
    console.error('Error canceling subscription:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No authorization token')) {
        return errorResponse('Authentication required', 401);
      }
      if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
        return errorResponse('Invalid or expired token', 401);
      }
      
      return errorResponse(error.message, 500);
    }
    
    return errorResponse('Internal server error', 500);
  }
};
