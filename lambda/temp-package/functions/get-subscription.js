"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const auth_1 = require("../utils/auth");
const dynamodb_1 = require("../utils/dynamodb");
const stripe_1 = require("../utils/stripe");
const handler = async (event) => {
    console.log('Get subscription request:', JSON.stringify(event, null, 2));
    if (event.httpMethod === 'OPTIONS') {
        return (0, auth_1.handleCORS)();
    }
    try {
        const cognitoUser = await (0, auth_1.getAuthenticatedUser)(event);
        const user = await dynamodb_1.userRepository.getUser(cognitoUser.sub);
        if (!user) {
            return (0, auth_1.errorResponse)('User not found', 404);
        }
        const subscription = await dynamodb_1.subscriptionRepository.getActiveSubscription(user.userId);
        if (!subscription) {
            return (0, auth_1.errorResponse)('No active subscription found', 404);
        }
        try {
            const stripeSubscription = await (0, stripe_1.getStripeSubscription)(subscription.stripeSubscriptionId);
            if (stripeSubscription.status !== subscription.status) {
                const updatedSubscription = await dynamodb_1.subscriptionRepository.updateSubscription(subscription.userId, subscription.subscriptionId, {
                    status: stripeSubscription.status,
                    currentPeriodStart: stripeSubscription.current_period_start,
                    currentPeriodEnd: stripeSubscription.current_period_end,
                    cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end || false,
                });
                return (0, auth_1.successResponse)(updatedSubscription);
            }
        }
        catch (stripeError) {
            console.warn('Failed to sync with Stripe, returning cached subscription:', stripeError);
        }
        return (0, auth_1.successResponse)(subscription);
    }
    catch (error) {
        console.error('Error getting subscription:', error);
        if (error instanceof Error) {
            if (error.message.includes('No authorization token')) {
                return (0, auth_1.errorResponse)('Authentication required', 401);
            }
            if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
                return (0, auth_1.errorResponse)('Invalid or expired token', 401);
            }
            return (0, auth_1.errorResponse)(error.message, 500);
        }
        return (0, auth_1.errorResponse)('Internal server error', 500);
    }
};
exports.handler = handler;
//# sourceMappingURL=get-subscription.js.map