"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const auth_1 = require("../utils/auth");
const dynamodb_1 = require("../utils/dynamodb");
const types_1 = require("../types");
const handler = async (event) => {
    console.log('Update usage request:', JSON.stringify(event, null, 2));
    if (event.httpMethod === 'OPTIONS') {
        return (0, auth_1.handleCORS)();
    }
    try {
        const cognitoUser = await (0, auth_1.getAuthenticatedUser)(event);
        const { searches = 0, apiCalls = 0, date } = (0, auth_1.validateRequestBody)(event, []);
        if (searches === 0 && apiCalls === 0) {
            return (0, auth_1.errorResponse)('At least one of searches or apiCalls must be greater than 0', 400);
        }
        const user = await dynamodb_1.userRepository.getUser(cognitoUser.sub);
        if (!user) {
            return (0, auth_1.errorResponse)('User not found', 404);
        }
        const usageDate = date || new Date().toISOString().split('T')[0];
        const planLimits = types_1.PLAN_LIMITS[user.plan] || types_1.PLAN_LIMITS.free;
        const currentUsage = await dynamodb_1.usageRepository.getUsage(user.userId, usageDate);
        const newSearches = (currentUsage?.searches || 0) + searches;
        const newApiCalls = (currentUsage?.apiCalls || 0) + apiCalls;
        if (planLimits.searchesPerDay !== -1 && newSearches > planLimits.searchesPerDay) {
            return (0, auth_1.errorResponse)(`Search limit exceeded. Daily limit: ${planLimits.searchesPerDay}, current usage: ${currentUsage?.searches || 0}`, 429);
        }
        if (planLimits.apiCallsPerMonth !== -1 && newApiCalls > (planLimits.apiCallsPerMonth / 30)) {
            return (0, auth_1.errorResponse)(`API call limit exceeded. Daily limit: ${Math.floor(planLimits.apiCallsPerMonth / 30)}, current usage: ${currentUsage?.apiCalls || 0}`, 429);
        }
        const updatedUsage = await dynamodb_1.usageRepository.updateUsage(user.userId, usageDate, {
            searches,
            apiCalls,
        });
        const searchUsagePercent = planLimits.searchesPerDay === -1
            ? 0
            : Math.min(100, (updatedUsage.searches / planLimits.searchesPerDay) * 100);
        const apiUsagePercent = planLimits.apiCallsPerMonth === -1
            ? 0
            : Math.min(100, (updatedUsage.apiCalls / (planLimits.apiCallsPerMonth / 30)) * 100);
        return (0, auth_1.successResponse)({
            usage: updatedUsage,
            limits: {
                searchesPerDay: planLimits.searchesPerDay,
                apiCallsPerMonth: planLimits.apiCallsPerMonth,
            },
            percentages: {
                searches: searchUsagePercent,
                apiCalls: apiUsagePercent,
            },
            remaining: {
                searches: planLimits.searchesPerDay === -1
                    ? -1
                    : Math.max(0, planLimits.searchesPerDay - updatedUsage.searches),
                apiCalls: planLimits.apiCallsPerMonth === -1
                    ? -1
                    : Math.max(0, Math.floor(planLimits.apiCallsPerMonth / 30) - updatedUsage.apiCalls),
            },
        });
    }
    catch (error) {
        console.error('Error updating usage:', error);
        if (error instanceof Error) {
            if (error.message.includes('No authorization token')) {
                return (0, auth_1.errorResponse)('Authentication required', 401);
            }
            if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
                return (0, auth_1.errorResponse)('Invalid or expired token', 401);
            }
            if (error.message.includes('Missing required field')) {
                return (0, auth_1.errorResponse)(error.message, 400);
            }
            return (0, auth_1.errorResponse)(error.message, 500);
        }
        return (0, auth_1.errorResponse)('Internal server error', 500);
    }
};
exports.handler = handler;
//# sourceMappingURL=update-usage.js.map