"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const auth_1 = require("../utils/auth");
const dynamodb_1 = require("../utils/dynamodb");
const stripe_1 = require("../utils/stripe");
const handler = async (event) => {
    console.log('Create portal session request:', JSON.stringify(event, null, 2));
    if (event.httpMethod === 'OPTIONS') {
        return (0, auth_1.handleCORS)();
    }
    try {
        const cognitoUser = await (0, auth_1.getAuthenticatedUser)(event);
        const { customerId, returnUrl } = (0, auth_1.validateRequestBody)(event, ['customerId']);
        const user = await dynamodb_1.userRepository.getUser(cognitoUser.sub);
        if (!user) {
            return (0, auth_1.errorResponse)('User not found', 404);
        }
        if (user.stripeCustomerId !== customerId) {
            return (0, auth_1.errorResponse)('Invalid customer ID', 403);
        }
        const session = await (0, stripe_1.createPortalSession)(customerId, returnUrl);
        return (0, auth_1.successResponse)({
            url: session.url,
        });
    }
    catch (error) {
        console.error('Error creating portal session:', error);
        if (error instanceof Error) {
            if (error.message.includes('No authorization token')) {
                return (0, auth_1.errorResponse)('Authentication required', 401);
            }
            if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
                return (0, auth_1.errorResponse)('Invalid or expired token', 401);
            }
            if (error.message.includes('Missing required field')) {
                return (0, auth_1.errorResponse)(error.message, 400);
            }
            return (0, auth_1.errorResponse)(error.message, 500);
        }
        return (0, auth_1.errorResponse)('Internal server error', 500);
    }
};
exports.handler = handler;
//# sourceMappingURL=create-portal-session.js.map