"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConfig = getConfig;
exports.validateConfig = validateConfig;
exports.getConfigInstance = getConfigInstance;
function getConfig() {
    const requiredEnvVars = [
        'STRIPE_SECRET_KEY',
        'STRIPE_WEBHOOK_SECRET',
        'DYNAMODB_USERS_TABLE',
        'DYNAMODB_SUBSCRIPTIONS_TABLE',
        'DYNAMODB_USAGE_TABLE',
        'DYNAMODB_INVOICES_TABLE',
        'SES_FROM_EMAIL',
        'COGNITO_USER_POOL_ID',
        'JWT_SECRET',
        'FRONTEND_URL',
        'API_URL'
    ];
    for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
            throw new Error(`Missing required environment variable: ${envVar}`);
        }
    }
    return {
        stripe: {
            secretKey: process.env.STRIPE_SECRET_KEY,
            webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
            priceIds: {
                professional: {
                    monthly: process.env.STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID || '',
                    annual: process.env.STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID || '',
                },
                enterprise: {
                    monthly: process.env.STRIPE_ENTERPRISE_MONTHLY_PRICE_ID || '',
                    annual: process.env.STRIPE_ENTERPRISE_ANNUAL_PRICE_ID || '',
                },
            },
        },
        aws: {
            region: process.env.AWS_REGION || 'us-east-1',
            dynamodb: {
                usersTable: process.env.DYNAMODB_USERS_TABLE,
                subscriptionsTable: process.env.DYNAMODB_SUBSCRIPTIONS_TABLE,
                usageTable: process.env.DYNAMODB_USAGE_TABLE,
                invoicesTable: process.env.DYNAMODB_INVOICES_TABLE,
            },
            ses: {
                fromEmail: process.env.SES_FROM_EMAIL,
                region: process.env.SES_REGION || process.env.AWS_REGION || 'us-east-1',
            },
            cognito: {
                userPoolId: process.env.COGNITO_USER_POOL_ID,
                region: process.env.AWS_REGION || 'us-east-1',
            },
        },
        jwt: {
            secret: process.env.JWT_SECRET,
        },
        app: {
            frontendUrl: process.env.FRONTEND_URL,
            apiUrl: process.env.API_URL,
        },
    };
}
function validateConfig(config) {
    if (!config.stripe.priceIds.professional.monthly || !config.stripe.priceIds.professional.annual) {
        console.warn('Professional plan price IDs not configured');
    }
    try {
        new URL(config.app.frontendUrl);
        new URL(config.app.apiUrl);
    }
    catch (error) {
        throw new Error('Invalid URL configuration');
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(config.aws.ses.fromEmail)) {
        throw new Error('Invalid SES from email format');
    }
}
let configInstance = null;
function getConfigInstance() {
    if (!configInstance) {
        configInstance = getConfig();
        validateConfig(configInstance);
    }
    return configInstance;
}
//# sourceMappingURL=config.js.map