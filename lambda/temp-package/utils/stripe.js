"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripe = void 0;
exports.createStripeCustomer = createStripeCustomer;
exports.createCheckoutSession = createCheckoutSession;
exports.createPortalSession = createPortalSession;
exports.getStripeSubscription = getStripeSubscription;
exports.cancelStripeSubscription = cancelStripeSubscription;
exports.getStripeCustomer = getStripeCustomer;
exports.getCustomerSubscriptions = getCustomerSubscriptions;
exports.verifyWebhookSignature = verifyWebhookSignature;
exports.getStripePrice = getStripePrice;
exports.getStripeProduct = getStripeProduct;
exports.mapStripeSubscription = mapStripeSubscription;
const stripe_1 = __importDefault(require("stripe"));
const config_1 = require("./config");
const config = (0, config_1.getConfigInstance)();
const stripe = new stripe_1.default(config.stripe.secretKey, {
    apiVersion: '2025-05-28.basil',
});
exports.stripe = stripe;
async function createStripeCustomer(email, name, metadata) {
    try {
        const customer = await stripe.customers.create({
            email,
            name,
            metadata: {
                source: 'gcandle-saas',
                ...metadata,
            },
        });
        return customer;
    }
    catch (error) {
        console.error('Error creating Stripe customer:', error);
        throw new Error('Failed to create Stripe customer');
    }
}
async function createCheckoutSession(priceId, customerId, successUrl, cancelUrl, metadata) {
    try {
        const sessionParams = {
            mode: 'subscription',
            payment_method_types: ['card'],
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            success_url: successUrl || `${config.app.frontendUrl}/profile?success=true&session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: cancelUrl || `${config.app.frontendUrl}/plan?canceled=true`,
            metadata: {
                source: 'gcandle-saas',
                ...metadata,
            },
            subscription_data: {
                metadata: {
                    source: 'gcandle-saas',
                    ...metadata,
                },
            },
        };
        if (customerId) {
            sessionParams.customer = customerId;
        }
        else {
            sessionParams.customer_creation = 'always';
        }
        const session = await stripe.checkout.sessions.create(sessionParams);
        return session;
    }
    catch (error) {
        console.error('Error creating checkout session:', error);
        throw new Error('Failed to create checkout session');
    }
}
async function createPortalSession(customerId, returnUrl) {
    try {
        const session = await stripe.billingPortal.sessions.create({
            customer: customerId,
            return_url: returnUrl || `${config.app.frontendUrl}/profile`,
        });
        return session;
    }
    catch (error) {
        console.error('Error creating portal session:', error);
        throw new Error('Failed to create portal session');
    }
}
async function getStripeSubscription(subscriptionId) {
    try {
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
        return subscription;
    }
    catch (error) {
        console.error('Error getting Stripe subscription:', error);
        throw new Error('Failed to get Stripe subscription');
    }
}
async function cancelStripeSubscription(subscriptionId, cancelAtPeriodEnd = true) {
    try {
        const subscription = await stripe.subscriptions.update(subscriptionId, {
            cancel_at_period_end: cancelAtPeriodEnd,
        });
        return subscription;
    }
    catch (error) {
        console.error('Error canceling Stripe subscription:', error);
        throw new Error('Failed to cancel Stripe subscription');
    }
}
async function getStripeCustomer(customerId) {
    try {
        const customer = await stripe.customers.retrieve(customerId);
        if (customer.deleted) {
            throw new Error('Customer has been deleted');
        }
        return customer;
    }
    catch (error) {
        console.error('Error getting Stripe customer:', error);
        throw new Error('Failed to get Stripe customer');
    }
}
async function getCustomerSubscriptions(customerId) {
    try {
        const subscriptions = await stripe.subscriptions.list({
            customer: customerId,
            status: 'all',
        });
        return subscriptions.data;
    }
    catch (error) {
        console.error('Error getting customer subscriptions:', error);
        throw new Error('Failed to get customer subscriptions');
    }
}
function verifyWebhookSignature(payload, signature) {
    try {
        const event = stripe.webhooks.constructEvent(payload, signature, config.stripe.webhookSecret);
        return event;
    }
    catch (error) {
        console.error('Error verifying webhook signature:', error);
        throw new Error('Invalid webhook signature');
    }
}
async function getStripePrice(priceId) {
    try {
        const price = await stripe.prices.retrieve(priceId);
        return price;
    }
    catch (error) {
        console.error('Error getting Stripe price:', error);
        throw new Error('Failed to get Stripe price');
    }
}
async function getStripeProduct(productId) {
    try {
        const product = await stripe.products.retrieve(productId);
        return product;
    }
    catch (error) {
        console.error('Error getting Stripe product:', error);
        throw new Error('Failed to get Stripe product');
    }
}
function mapStripeSubscription(stripeSubscription, userId) {
    const priceId = stripeSubscription.items.data[0]?.price.id || '';
    let planId = 'unknown';
    let planName = 'Unknown Plan';
    if (priceId === config.stripe.priceIds.professional.monthly ||
        priceId === config.stripe.priceIds.professional.annual) {
        planId = 'professional';
        planName = 'Professional';
    }
    else if (priceId === config.stripe.priceIds.enterprise.monthly ||
        priceId === config.stripe.priceIds.enterprise.annual) {
        planId = 'enterprise';
        planName = 'Enterprise';
    }
    return {
        subscriptionId: `sub_${userId}_${Date.now()}`,
        userId,
        stripeSubscriptionId: stripeSubscription.id,
        stripeCustomerId: stripeSubscription.customer,
        status: stripeSubscription.status,
        planId,
        planName,
        priceId,
        currentPeriodStart: stripeSubscription.current_period_start,
        currentPeriodEnd: stripeSubscription.current_period_end,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end || false,
        trialEnd: stripeSubscription.trial_end || undefined,
    };
}
//# sourceMappingURL=stripe.js.map