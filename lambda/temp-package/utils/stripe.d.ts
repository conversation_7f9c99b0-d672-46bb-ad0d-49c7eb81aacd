import Stripe from 'stripe';
declare const stripe: Stripe;
export declare function createStripeCustomer(email: string, name?: string, metadata?: Record<string, string>): Promise<Stripe.Customer>;
export declare function createCheckoutSession(priceId: string, customerId?: string, successUrl?: string, cancelUrl?: string, metadata?: Record<string, string>): Promise<Stripe.Checkout.Session>;
export declare function createPortalSession(customerId: string, returnUrl?: string): Promise<Stripe.BillingPortal.Session>;
export declare function getStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription>;
export declare function cancelStripeSubscription(subscriptionId: string, cancelAtPeriodEnd?: boolean): Promise<Stripe.Subscription>;
export declare function getStripeCustomer(customerId: string): Promise<Stripe.Customer>;
export declare function getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]>;
export declare function verifyWebhookSignature(payload: string | Buffer, signature: string): Stripe.Event;
export declare function getStripePrice(priceId: string): Promise<Stripe.Price>;
export declare function getStripeProduct(productId: string): Promise<Stripe.Product>;
export declare function mapStripeSubscription(stripeSubscription: Stripe.Subscription, userId: string): {
    subscriptionId: string;
    userId: string;
    stripeSubscriptionId: string;
    stripeCustomerId: string;
    status: string;
    planId: string;
    planName: string;
    priceId: string;
    currentPeriodStart: number;
    currentPeriodEnd: number;
    cancelAtPeriodEnd: boolean;
    trialEnd?: number;
};
export { stripe };
//# sourceMappingURL=stripe.d.ts.map