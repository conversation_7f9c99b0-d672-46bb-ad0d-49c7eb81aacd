"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.usageRepository = exports.subscriptionRepository = exports.userRepository = exports.UsageRepository = exports.SubscriptionRepository = exports.UserRepository = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const config_1 = require("./config");
const config = (0, config_1.getConfigInstance)();
const client = new client_dynamodb_1.DynamoDBClient({ region: config.aws.region });
const docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(client);
class UserRepository {
    constructor() {
        this.tableName = config.aws.dynamodb.usersTable;
    }
    async getUser(userId) {
        try {
            const result = await docClient.send(new lib_dynamodb_1.GetCommand({
                TableName: this.tableName,
                Key: { userId },
            }));
            return result.Item || null;
        }
        catch (error) {
            console.error('Error getting user:', error);
            throw new Error('Failed to get user');
        }
    }
    async createUser(user) {
        const now = new Date().toISOString();
        const newUser = {
            ...user,
            createdAt: now,
            updatedAt: now,
        };
        try {
            await docClient.send(new lib_dynamodb_1.PutCommand({
                TableName: this.tableName,
                Item: newUser,
                ConditionExpression: 'attribute_not_exists(userId)',
            }));
            return newUser;
        }
        catch (error) {
            if (error.name === 'ConditionalCheckFailedException') {
                throw new Error('User already exists');
            }
            console.error('Error creating user:', error);
            throw new Error('Failed to create user');
        }
    }
    async updateUser(userId, updates) {
        const now = new Date().toISOString();
        const updateExpressions = [];
        const expressionAttributeNames = {};
        const expressionAttributeValues = {};
        Object.entries(updates).forEach(([key, value]) => {
            if (key !== 'userId' && key !== 'createdAt') {
                updateExpressions.push(`#${key} = :${key}`);
                expressionAttributeNames[`#${key}`] = key;
                expressionAttributeValues[`:${key}`] = value;
            }
        });
        updateExpressions.push('#updatedAt = :updatedAt');
        expressionAttributeNames['#updatedAt'] = 'updatedAt';
        expressionAttributeValues[':updatedAt'] = now;
        try {
            const result = await docClient.send(new lib_dynamodb_1.UpdateCommand({
                TableName: this.tableName,
                Key: { userId },
                UpdateExpression: `SET ${updateExpressions.join(', ')}`,
                ExpressionAttributeNames: expressionAttributeNames,
                ExpressionAttributeValues: expressionAttributeValues,
                ReturnValues: 'ALL_NEW',
            }));
            return result.Attributes;
        }
        catch (error) {
            console.error('Error updating user:', error);
            throw new Error('Failed to update user');
        }
    }
    async getUserByEmail(email) {
        try {
            const result = await docClient.send(new lib_dynamodb_1.ScanCommand({
                TableName: this.tableName,
                FilterExpression: 'email = :email',
                ExpressionAttributeValues: {
                    ':email': email,
                },
            }));
            return result.Items?.[0] || null;
        }
        catch (error) {
            console.error('Error getting user by email:', error);
            throw new Error('Failed to get user by email');
        }
    }
    async getUserByStripeCustomerId(stripeCustomerId) {
        try {
            const result = await docClient.send(new lib_dynamodb_1.ScanCommand({
                TableName: this.tableName,
                FilterExpression: 'stripeCustomerId = :stripeCustomerId',
                ExpressionAttributeValues: {
                    ':stripeCustomerId': stripeCustomerId,
                },
            }));
            return result.Items?.[0] || null;
        }
        catch (error) {
            console.error('Error getting user by Stripe customer ID:', error);
            throw new Error('Failed to get user by Stripe customer ID');
        }
    }
}
exports.UserRepository = UserRepository;
class SubscriptionRepository {
    constructor() {
        this.tableName = config.aws.dynamodb.subscriptionsTable;
    }
    async getSubscription(userId, subscriptionId) {
        try {
            const result = await docClient.send(new lib_dynamodb_1.GetCommand({
                TableName: this.tableName,
                Key: { userId, subscriptionId },
            }));
            return result.Item || null;
        }
        catch (error) {
            console.error('Error getting subscription:', error);
            throw new Error('Failed to get subscription');
        }
    }
    async getUserSubscriptions(userId) {
        try {
            const result = await docClient.send(new lib_dynamodb_1.QueryCommand({
                TableName: this.tableName,
                KeyConditionExpression: 'userId = :userId',
                ExpressionAttributeValues: {
                    ':userId': userId,
                },
            }));
            return result.Items || [];
        }
        catch (error) {
            console.error('Error getting user subscriptions:', error);
            throw new Error('Failed to get user subscriptions');
        }
    }
    async getActiveSubscription(userId) {
        const subscriptions = await this.getUserSubscriptions(userId);
        return subscriptions.find(sub => sub.status === 'active' || sub.status === 'trialing') || null;
    }
    async createSubscription(subscription) {
        const now = new Date().toISOString();
        const newSubscription = {
            ...subscription,
            createdAt: now,
            updatedAt: now,
        };
        try {
            await docClient.send(new lib_dynamodb_1.PutCommand({
                TableName: this.tableName,
                Item: newSubscription,
            }));
            return newSubscription;
        }
        catch (error) {
            console.error('Error creating subscription:', error);
            throw new Error('Failed to create subscription');
        }
    }
    async updateSubscription(userId, subscriptionId, updates) {
        const now = new Date().toISOString();
        const updateExpressions = [];
        const expressionAttributeNames = {};
        const expressionAttributeValues = {};
        Object.entries(updates).forEach(([key, value]) => {
            if (key !== 'userId' && key !== 'subscriptionId' && key !== 'createdAt') {
                updateExpressions.push(`#${key} = :${key}`);
                expressionAttributeNames[`#${key}`] = key;
                expressionAttributeValues[`:${key}`] = value;
            }
        });
        updateExpressions.push('#updatedAt = :updatedAt');
        expressionAttributeNames['#updatedAt'] = 'updatedAt';
        expressionAttributeValues[':updatedAt'] = now;
        try {
            const result = await docClient.send(new lib_dynamodb_1.UpdateCommand({
                TableName: this.tableName,
                Key: { userId, subscriptionId },
                UpdateExpression: `SET ${updateExpressions.join(', ')}`,
                ExpressionAttributeNames: expressionAttributeNames,
                ExpressionAttributeValues: expressionAttributeValues,
                ReturnValues: 'ALL_NEW',
            }));
            return result.Attributes;
        }
        catch (error) {
            console.error('Error updating subscription:', error);
            throw new Error('Failed to update subscription');
        }
    }
    async getSubscriptionByStripeId(stripeSubscriptionId) {
        try {
            const result = await docClient.send(new lib_dynamodb_1.ScanCommand({
                TableName: this.tableName,
                FilterExpression: 'stripeSubscriptionId = :stripeSubscriptionId',
                ExpressionAttributeValues: {
                    ':stripeSubscriptionId': stripeSubscriptionId,
                },
            }));
            return result.Items?.[0] || null;
        }
        catch (error) {
            console.error('Error getting subscription by Stripe ID:', error);
            throw new Error('Failed to get subscription by Stripe ID');
        }
    }
}
exports.SubscriptionRepository = SubscriptionRepository;
class UsageRepository {
    constructor() {
        this.tableName = config.aws.dynamodb.usageTable;
    }
    async getUsage(userId, date) {
        try {
            const result = await docClient.send(new lib_dynamodb_1.GetCommand({
                TableName: this.tableName,
                Key: { userId, date },
            }));
            return result.Item || null;
        }
        catch (error) {
            console.error('Error getting usage:', error);
            throw new Error('Failed to get usage');
        }
    }
    async updateUsage(userId, date, updates) {
        const now = new Date().toISOString();
        try {
            const result = await docClient.send(new lib_dynamodb_1.UpdateCommand({
                TableName: this.tableName,
                Key: { userId, date },
                UpdateExpression: 'SET searches = if_not_exists(searches, :zero) + :searchIncrement, apiCalls = if_not_exists(apiCalls, :zero) + :apiIncrement, lastUpdated = :lastUpdated',
                ExpressionAttributeValues: {
                    ':zero': 0,
                    ':searchIncrement': updates.searches || 0,
                    ':apiIncrement': updates.apiCalls || 0,
                    ':lastUpdated': now,
                },
                ReturnValues: 'ALL_NEW',
            }));
            return result.Attributes;
        }
        catch (error) {
            console.error('Error updating usage:', error);
            throw new Error('Failed to update usage');
        }
    }
}
exports.UsageRepository = UsageRepository;
exports.userRepository = new UserRepository();
exports.subscriptionRepository = new SubscriptionRepository();
exports.usageRepository = new UsageRepository();
//# sourceMappingURL=dynamodb.js.map