import { APIGatewayProxyEvent } from 'aws-lambda';
import { CognitoUser, JWTPayload } from '../types';
export declare function extractToken(event: APIGatewayProxyEvent): string | null;
export declare function verifyToken(token: string): Promise<JWTPayload>;
export declare function getAuthenticatedUser(event: APIGatewayProxyEvent): Promise<CognitoUser>;
export declare function createResponse(statusCode: number, body: any, headers?: Record<string, string>): {
    statusCode: number;
    headers: {
        'Content-Type': string;
        'Access-Control-Allow-Origin': string;
        'Access-Control-Allow-Headers': string;
        'Access-Control-Allow-Methods': string;
    };
    body: string;
};
export declare function successResponse(data: any, statusCode?: number): {
    statusCode: number;
    headers: {
        'Content-Type': string;
        'Access-Control-Allow-Origin': string;
        'Access-Control-Allow-Headers': string;
        'Access-Control-Allow-Methods': string;
    };
    body: string;
};
export declare function errorResponse(error: string, statusCode?: number, details?: any): {
    statusCode: number;
    headers: {
        'Content-Type': string;
        'Access-Control-Allow-Origin': string;
        'Access-Control-Allow-Headers': string;
        'Access-Control-Allow-Methods': string;
    };
    body: string;
};
export declare function handleCORS(): {
    statusCode: number;
    headers: {
        'Content-Type': string;
        'Access-Control-Allow-Origin': string;
        'Access-Control-Allow-Headers': string;
        'Access-Control-Allow-Methods': string;
    };
    body: string;
};
export declare function validateRequestBody<T extends object>(event: APIGatewayProxyEvent, requiredFields: (keyof T)[]): T;
export declare function getUserId(event: APIGatewayProxyEvent): Promise<string>;
export declare function checkResourcePermission(requestedUserId: string, authenticatedUserId: string): void;
//# sourceMappingURL=auth.d.ts.map