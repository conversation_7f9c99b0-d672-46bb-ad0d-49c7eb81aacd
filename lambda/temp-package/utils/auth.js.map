{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": ";;;;;AAaA,oCAaC;AAgBD,kCAaC;AAKD,oDAeC;AAKD,wCAgBC;AAKD,0CAKC;AAKD,sCAYC;AAKD,gCAMC;AAKD,kDAuBC;AAKD,8BAGC;AAKD,0DAOC;AAlLD,wDAAkC;AAClC,gEAA+B;AAQ/B,SAAgB,YAAY,CAAC,KAA2B;IACtD,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC;IAE9E,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAGD,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACrC,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAKD,MAAM,MAAM,GAAG,IAAA,kBAAU,EAAC;IACxB,OAAO,EAAE,uBAAuB,OAAO,CAAC,GAAG,CAAC,UAAU,kBAAkB,OAAO,CAAC,GAAG,CAAC,oBAAoB,wBAAwB;CACjI,CAAC,CAAC;AAEH,SAAS,MAAM,CAAC,MAAW,EAAE,QAAa;IACxC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC5C,MAAM,UAAU,GAAG,GAAG,EAAE,YAAY,EAAE,CAAC;QACvC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,WAAW,CAAC,KAAa;IACvC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;YACxB,UAAU,EAAE,CAAC,OAAO,CAAC;YACrB,MAAM,EAAE,uBAAuB,OAAO,CAAC,GAAG,CAAC,UAAU,kBAAkB,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE;SAC1G,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YAClB,IAAI,GAAG,EAAE,CAAC;gBACR,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,OAAqB,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAKM,KAAK,UAAU,oBAAoB,CAAC,KAA2B;IACpE,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IAElC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAEzC,OAAO;QACL,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,KAAK;KAChD,CAAC;AACJ,CAAC;AAKD,SAAgB,cAAc,CAC5B,UAAkB,EAClB,IAAS,EACT,UAAkC,EAAE;IAEpC,OAAO;QACL,UAAU;QACV,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,6BAA6B,EAAE,GAAG;YAClC,8BAA8B,EAAE,4BAA4B;YAC5D,8BAA8B,EAAE,6BAA6B;YAC7D,GAAG,OAAO;SACX;QACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AACJ,CAAC;AAKD,SAAgB,eAAe,CAAC,IAAS,EAAE,aAAqB,GAAG;IACjE,OAAO,cAAc,CAAC,UAAU,EAAE;QAChC,OAAO,EAAE,IAAI;QACb,IAAI;KACL,CAAC,CAAC;AACL,CAAC;AAKD,SAAgB,aAAa,CAC3B,KAAa,EACb,aAAqB,GAAG,EACxB,OAAa;IAEb,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAE5C,OAAO,cAAc,CAAC,UAAU,EAAE;QAChC,OAAO,EAAE,KAAK;QACd,KAAK;QACL,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;KAC5B,CAAC,CAAC;AACL,CAAC;AAKD,SAAgB,UAAU;IACxB,OAAO,cAAc,CAAC,GAAG,EAAE,EAAE,EAAE;QAC7B,6BAA6B,EAAE,GAAG;QAClC,8BAA8B,EAAE,4BAA4B;QAC5D,8BAA8B,EAAE,6BAA6B;KAC9D,CAAC,CAAC;AACL,CAAC;AAKD,SAAgB,mBAAmB,CACjC,KAA2B,EAC3B,cAA2B;IAE3B,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,IAAO,CAAC;IACZ,IAAI,CAAC;QACH,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC;IAGD,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;QACnC,IAAI,CAAC,CAAC,KAAK,IAAK,IAAe,CAAC,IAAK,IAAY,CAAC,KAAK,CAAC,KAAK,SAAS,IAAK,IAAY,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACxG,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAKM,KAAK,UAAU,SAAS,CAAC,KAA2B;IACzD,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,KAAK,CAAC,CAAC;IAC/C,OAAO,IAAI,CAAC,GAAG,CAAC;AAClB,CAAC;AAKD,SAAgB,uBAAuB,CACrC,eAAuB,EACvB,mBAA2B;IAE3B,IAAI,eAAe,KAAK,mBAAmB,EAAE,CAAC;QAC5C,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtE,CAAC;AACH,CAAC"}