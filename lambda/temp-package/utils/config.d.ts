export interface Config {
    stripe: {
        secretKey: string;
        webhookSecret: string;
        priceIds: {
            professional: {
                monthly: string;
                annual: string;
            };
            enterprise: {
                monthly: string;
                annual: string;
            };
        };
    };
    aws: {
        region: string;
        dynamodb: {
            usersTable: string;
            subscriptionsTable: string;
            usageTable: string;
            invoicesTable: string;
        };
        ses: {
            fromEmail: string;
            region: string;
        };
        cognito: {
            userPoolId: string;
            region: string;
        };
    };
    jwt: {
        secret: string;
    };
    app: {
        frontendUrl: string;
        apiUrl: string;
    };
}
export declare function getConfig(): Config;
export declare function validateConfig(config: Config): void;
export declare function getConfigInstance(): Config;
//# sourceMappingURL=config.d.ts.map