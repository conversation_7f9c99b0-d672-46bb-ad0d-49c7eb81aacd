"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractToken = extractToken;
exports.verifyToken = verifyToken;
exports.getAuthenticatedUser = getAuthenticatedUser;
exports.createResponse = createResponse;
exports.successResponse = successResponse;
exports.errorResponse = errorResponse;
exports.handleCORS = handleCORS;
exports.validateRequestBody = validateRequestBody;
exports.getUserId = getUserId;
exports.checkResourcePermission = checkResourcePermission;
const jwks_rsa_1 = __importDefault(require("jwks-rsa"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
function extractToken(event) {
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
        return null;
    }
    if (authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }
    return authHeader;
}
const client = (0, jwks_rsa_1.default)({
    jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.COGNITO_USER_POOL_ID}/.well-known/jwks.json`
});
function getKey(header, callback) {
    client.getSigningKey(header.kid, (err, key) => {
        const signingKey = key?.getPublicKey();
        callback(null, signingKey);
    });
}
function verifyToken(token) {
    return new Promise((resolve, reject) => {
        jsonwebtoken_1.default.verify(token, getKey, {
            algorithms: ['RS256'],
            issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.COGNITO_USER_POOL_ID}`,
        }, (err, decoded) => {
            if (err) {
                reject(new Error('Invalid token'));
            }
            else {
                resolve(decoded);
            }
        });
    });
}
async function getAuthenticatedUser(event) {
    const token = extractToken(event);
    if (!token) {
        throw new Error('No authorization token provided');
    }
    const payload = await verifyToken(token);
    return {
        sub: payload.sub,
        email: payload.email,
        name: payload.name,
        email_verified: payload.email_verified || false,
    };
}
function createResponse(statusCode, body, headers = {}) {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            ...headers,
        },
        body: JSON.stringify(body),
    };
}
function successResponse(data, statusCode = 200) {
    return createResponse(statusCode, {
        success: true,
        data,
    });
}
function errorResponse(error, statusCode = 400, details) {
    console.error('API Error:', error, details);
    return createResponse(statusCode, {
        success: false,
        error,
        ...(details && { details }),
    });
}
function handleCORS() {
    return createResponse(200, {}, {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,Authorization',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    });
}
function validateRequestBody(event, requiredFields) {
    if (!event.body) {
        throw new Error('Request body is required');
    }
    let body;
    try {
        body = JSON.parse(event.body);
    }
    catch (error) {
        throw new Error('Invalid JSON in request body');
    }
    for (const field of requiredFields) {
        if (!(field in body) || body[field] === undefined || body[field] === null) {
            throw new Error(`Missing required field: ${String(field)}`);
        }
    }
    return body;
}
async function getUserId(event) {
    const user = await getAuthenticatedUser(event);
    return user.sub;
}
function checkResourcePermission(requestedUserId, authenticatedUserId) {
    if (requestedUserId !== authenticatedUserId) {
        throw new Error('Insufficient permissions to access this resource');
    }
}
//# sourceMappingURL=auth.js.map