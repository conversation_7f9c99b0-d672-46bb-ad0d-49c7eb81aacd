// Auto-generated research data
export const researchData = [
  {
    "slug": "darkhotel-rpc-attack-componentsssss",
    "title": "Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\r\n\r\n## 1.1. **Background**\r\n\r\n### Organization Introduction\r\n\r\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.\r\n\r\n### Event Overview\r\n\r\nRecently, the Knownsec 404 Advanced Threat Intelligence Team captured another attack activity from the DarkHotel organization during hunting. The attack activity utilized Microsoft-signed system programs to side-load malicious DLL files, ultimately leading to the decryption and loading of malicious components. Compared to the InitPlugins framework used by the organization discovered in 2023, the framework has been upgraded in this attack activity.\r\n\r\n### Core Findings\r\n\r\nDarkHotel continues to demonstrate a high standard in attack methods and techniques. Even with active defense and antivirus software running on victim hosts, they can still carry out attacks effortlessly, showing their high level of anti-detection capabilities.\r\n\r\nThe attackers are proficient in MFC-related loading mechanisms and possess extremely strong coding abilities. They used various obfuscation techniques and a dual-layer injection mechanism in their code, which not only increases the difficulty of analysis but also hides their tracks while ensuring the stable operation of the injected code.\r\n\r\nIn terms of functional design, they adopted a modular loading approach, disguising encrypted module files as system files. To achieve convincing deception, they even modified the timestamp of the disguised files to match the timestamp of the system file kernel32.dll (normally, file times are modified during system installation or system updates).\r\n\r\nThe attackers also utilized RPC technology to execute component functions, separating communication components from functional components, with the two using different scheduled tasks to maintain persistence.\r\n\r\n## 1.2 Technical Analysis\r\n\r\n### Attack Flow\r\n\r\n![image-20250411152420501](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411152420501.png)\r\n\r\n### Key Technologies\r\n\r\n**MFC Local Resource Loading Mechanism**\r\n\r\nThere is a localization resource loading mechanism in MFC, where when an MFC-written exe or dll (let's call it MFCxx.exe) loads resources, it doesn't directly load its own resources but looks for resource dlls in the following order:\r\n\r\n1. The current user's default UI language, returned from the GetUserDefaultUILanguage() Win32 API. For example, if it's FRC (Canadian French), it loads MFCxxFRC.dll.\r\n\r\n![image-20250410135119646](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250410135119646.png)\r\n\r\n2. The current user's default UI language without any specific sublanguage, then it loads MFCxxFRA.dll.\r\n\r\n3. The system's default UI language, returned by the GetSystemDefaultUILanguage() API. On other platforms, this is the language of the operating system itself. For example, if the system language is ENC (Canadian English), it loads MFCxxENC.dll.\r\n\r\n4. The system's default UI language without any specific sublanguage, then it loads MFCxxENU.dll.\r\n\r\n5. A fake language with a 3-letter code LOC, thus loading MFCxxLOC.dll.\r\n\r\n![image-20250410135138846](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250410135138846.png)\r\n\r\nThe directory structure of the scheduled task's main program is as shown below. The attacker released LOC.dll to the main program's directory, causing this dll to be loaded and executed:\r\n\r\n![image-20250410140425126](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250410140425126.png)\r\n\r\n**Code Obfuscation**\r\n\r\nThe attackers used multiple types of obfuscated code, causing the code to bloat and greatly increasing the difficulty of analysis. One of the obfuscated codes is as follows:\r\n\r\n![img](/research/darkhotel-rpc-attack-componentsssss/assets/wps1.jpg)  \r\n\r\n**Double Injection**\r\n\r\nFirst injection: By searching for specified system processes and injecting code related to process creation, they obtain higher privileges and avoid injection failure:\r\n\r\n![img](/research/darkhotel-rpc-attack-componentsssss/assets/wps3.jpg) \r\n\r\nSecond injection: Injecting the malicious payload into the system process started by the first injection:\r\n\r\n![img](/research/darkhotel-rpc-attack-componentsssss/assets/wps4.jpg) \r\n\r\n**System File Disguise**\r\n\r\nThe attacker embedded a list of files to be decrypted, all of which were encrypted ciphertexts. From the file paths and filenames in the list, it's clear that the attacker intentionally disguised the related files as system key files. To increase file credibility, they read the file timestamp of kernel32.dll on the current host and aligned the timestamps of all existing .pem files in the file list with that of kernel32.dll.\r\n\r\n![image-20250410140554012](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250410140554012.png)\r\n\r\n**Local RPC Call Mechanism**\r\n\r\nFunctional component registering RPC interface:\r\n\r\n![image-20250411103553450](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411103553450.png)\r\n\r\nCore loading component calling interface:\r\n\r\n![image-20250411103915253](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411103915253.png)\r\n\r\n### Component Analysis\r\n\r\n**Core Loading Component**\r\n\r\nThe attacker designed multiple types of component loading methods in the core loading component, which can be classified based on the type value set by the attacker:\r\n\r\nType 1: Create thread to execute shellcode:\r\n\r\n![image-20250411105420360](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411105420360.png)\r\n\r\nType 2: Reflective loading:\r\n\r\n![image-20250411105731119](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411105731119.png)\r\n\r\nType 3: Injection execution:\r\n\r\n![image-20250411110549996](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411110549996.png)\r\n\r\nType 4: Using LoadLibraryW for loading:\r\n\r\n![image-20250411110748871](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411110748871.png)\r\n\r\nThe first three types need to be decrypted using the same algorithm and key:\r\n\r\n![image-20250411111006586](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411111006586.png)\r\n\r\n**Functional Components**\r\n\r\nA total of 4 components were captured, divided into two types, as shown in the table below:\r\n\r\n![image-20250411143258480](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411143258480.png)\r\n\r\n### **Attribution**\r\n\r\n**Algorithm Association**\r\n\r\nIn previously exposed attack incidents, DarkHotel has repeatedly used XOR algorithms for encryption and decryption. For example, the algorithm used in the organization's attack activity using the initplugins architecture in 2023:\r\n\r\n![image-20250411142210095](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411142210095.png)\r\n\r\n**Architecture Association**\r\n\r\nThe component loading framework used by DarkHotel in this incident and the initplugins framework captured in 2023 both decrypt and load by reading built-in file lists:\r\n\r\n![image-20250411143814970](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411143814970.png)\r\n\r\n**Component Association**\r\n\r\nThe components loaded in this captured attack activity are identical to those used in the 2023 initplugins attack activity.\r\n\r\n![image-20250411152908026](/research/darkhotel-rpc-attack-componentsssss/assets/image-20250411152908026.png)\r\n\r\nBased on the above associations, we are highly confident that the samples captured this time are the work of the DarkHotel organization. Compared to previous attack activities of this organization, this time DarkHotel has separated the remote control component (meterpreter) from functional components (keyboard logging, screen capture, and USB theft) for separate loading and operation. This may be to prevent the failure of the entire attack chain due to a single component being discovered, showing that the organization has been consistently working to improve the robustness of their weapons and increase the chances of successful attacks.\r\n\r\n## 1.4 IOC\r\n\r\ne14459863ac38df87e59e0a93a0fa438\r\n\r\nd61308ddf2624e726fe83cd487dd6fe3\r\n\r\nc92afb4d4fc5effd49cbc048d4355d1c\r\n\r\n7cff54d8227393c352ee0859bc5cf01e",
    "date": "July 29, 2025",
    "author": "Knownsec 404 Team",
    "authorRole": "Advanced Threat Intelligence Team",
    "image": "/img/home/<USER>",
    "readTime": "5 min read",
    "category": "Threat Intelligence",
    "tags": [
      "Threat Intelligence",
      "Security Research"
    ]
  },
  {
    "slug": "test_article",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\r\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\r\n\r\n## 1.1. **Background**\r\n\r\n### Organization Introduction\r\n\r\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.\r\n\r\n### Event Overview\r\n\r\nRecently, the Knownsec 404 Advanced Threat Intelligence Team captured another attack activity from the DarkHotel organization during hunting. The attack activity utilized Microsoft-signed system programs to side-load malicious DLL files, ultimately leading to the decryption and loading of malicious components. Compared to the InitPlugins framework used by the organization discovered in 2023, the framework has been upgraded in this attack activity.\r\n\r\n### Core Findings\r\n\r\nDarkHotel continues to demonstrate a high standard in attack methods and techniques. Even with active defense and antivirus software running on victim hosts, they can still carry out attacks effortlessly, showing their high level of anti-detection capabilities.\r\n\r\nThe attackers are proficient in MFC-related loading mechanisms and possess extremely strong coding abilities. They used various obfuscation techniques and a dual-layer injection mechanism in their code, which not only increases the difficulty of analysis but also hides their tracks while ensuring the stable operation of the injected code.\r\n\r\nIn terms of functional design, they adopted a modular loading approach, disguising encrypted module files as system files. To achieve convincing deception, they even modified the timestamp of the disguised files to match the timestamp of the system file kernel32.dll (normally, file times are modified during system installation or system updates).\r\n\r\nThe attackers also utilized RPC technology to execute component functions, separating communication components from functional components, with the two using different scheduled tasks to maintain persistence.\r\n\r\n## 1.2 Technical Analysis\r\n\r\n### Attack Flow\r\n\r\n![image-20250411152420501](/research/test_article/assets/image-20250411152420501.png)\r\n\r\n### Key Technologies\r\n\r\n**MFC Local Resource Loading Mechanism**\r\n\r\nThere is a localization resource loading mechanism in MFC, where when an MFC-written exe or dll (let's call it MFCxx.exe) loads resources, it doesn't directly load its own resources but looks for resource dlls in the following order:\r\n\r\n1. The current user's default UI language, returned from the GetUserDefaultUILanguage() Win32 API. For example, if it's FRC (Canadian French), it loads MFCxxFRC.dll.\r\n\r\n![image-20250410135119646](/research/test_article/assets/image-20250410135119646.png)\r\n\r\n2. The current user's default UI language without any specific sublanguage, then it loads MFCxxFRA.dll.\r\n\r\n3. The system's default UI language, returned by the GetSystemDefaultUILanguage() API. On other platforms, this is the language of the operating system itself. For example, if the system language is ENC (Canadian English), it loads MFCxxENC.dll.\r\n\r\n4. The system's default UI language without any specific sublanguage, then it loads MFCxxENU.dll.\r\n\r\n5. A fake language with a 3-letter code LOC, thus loading MFCxxLOC.dll.\r\n\r\n![image-20250410135138846](/research/test_article/assets/image-20250410135138846.png)\r\n\r\nThe directory structure of the scheduled task's main program is as shown below. The attacker released LOC.dll to the main program's directory, causing this dll to be loaded and executed:\r\n\r\n![image-20250410140425126](/research/test_article/assets/image-20250410140425126.png)\r\n\r\n**Code Obfuscation**\r\n\r\nThe attackers used multiple types of obfuscated code, causing the code to bloat and greatly increasing the difficulty of analysis. One of the obfuscated codes is as follows:\r\n\r\n![img](/research/test_article/assets/wps1.jpg)  \r\n\r\n**Double Injection**\r\n\r\nFirst injection: By searching for specified system processes and injecting code related to process creation, they obtain higher privileges and avoid injection failure:\r\n\r\n![img](/research/test_article/assets/wps3.jpg) \r\n\r\nSecond injection: Injecting the malicious payload into the system process started by the first injection:\r\n\r\n![img](/research/test_article/assets/wps4.jpg) \r\n\r\n**System File Disguise**\r\n\r\nThe attacker embedded a list of files to be decrypted, all of which were encrypted ciphertexts. From the file paths and filenames in the list, it's clear that the attacker intentionally disguised the related files as system key files. To increase file credibility, they read the file timestamp of kernel32.dll on the current host and aligned the timestamps of all existing .pem files in the file list with that of kernel32.dll.\r\n\r\n![image-20250410140554012](/research/test_article/assets/image-20250410140554012.png)\r\n\r\n**Local RPC Call Mechanism**\r\n\r\nFunctional component registering RPC interface:\r\n\r\n![image-20250411103553450](/research/test_article/assets/image-20250411103553450.png)\r\n\r\nCore loading component calling interface:\r\n\r\n![image-20250411103915253](/research/test_article/assets/image-20250411103915253.png)\r\n\r\n### Component Analysis\r\n\r\n**Core Loading Component**\r\n\r\nThe attacker designed multiple types of component loading methods in the core loading component, which can be classified based on the type value set by the attacker:\r\n\r\nType 1: Create thread to execute shellcode:\r\n\r\n![image-20250411105420360](/research/test_article/assets/image-20250411105420360.png)\r\n\r\nType 2: Reflective loading:\r\n\r\n![image-20250411105731119](/research/test_article/assets/image-20250411105731119.png)\r\n\r\nType 3: Injection execution:\r\n\r\n![image-20250411110549996](/research/test_article/assets/image-20250411110549996.png)\r\n\r\nType 4: Using LoadLibraryW for loading:\r\n\r\n![image-20250411110748871](/research/test_article/assets/image-20250411110748871.png)\r\n\r\nThe first three types need to be decrypted using the same algorithm and key:\r\n\r\n![image-20250411111006586](/research/test_article/assets/image-20250411111006586.png)\r\n\r\n**Functional Components**\r\n\r\nA total of 4 components were captured, divided into two types, as shown in the table below:\r\n\r\n![image-20250411143258480](/research/test_article/assets/image-20250411143258480.png)\r\n\r\n### **Attribution**\r\n\r\n**Algorithm Association**\r\n\r\nIn previously exposed attack incidents, DarkHotel has repeatedly used XOR algorithms for encryption and decryption. For example, the algorithm used in the organization's attack activity using the initplugins architecture in 2023:\r\n\r\n![image-20250411142210095](/research/test_article/assets/image-20250411142210095.png)\r\n\r\n**Architecture Association**\r\n\r\nThe component loading framework used by DarkHotel in this incident and the initplugins framework captured in 2023 both decrypt and load by reading built-in file lists:\r\n\r\n![image-20250411143814970](/research/test_article/assets/image-20250411143814970.png)\r\n\r\n**Component Association**\r\n\r\nThe components loaded in this captured attack activity are identical to those used in the 2023 initplugins attack activity.\r\n\r\n![image-20250411152908026](/research/test_article/assets/image-20250411152908026.png)\r\n\r\nBased on the above associations, we are highly confident that the samples captured this time are the work of the DarkHotel organization. Compared to previous attack activities of this organization, this time DarkHotel has separated the remote control component (meterpreter) from functional components (keyboard logging, screen capture, and USB theft) for separate loading and operation. This may be to prevent the failure of the entire attack chain due to a single component being discovered, showing that the organization has been consistently working to improve the robustness of their weapons and increase the chances of successful attacks.\r\n\r\n## 1.4 IOC\r\n\r\ne14459863ac38df87e59e0a93a0fa438\r\n\r\nd61308ddf2624e726fe83cd487dd6fe3\r\n\r\nc92afb4d4fc5effd49cbc048d4355d1c\r\n\r\n7cff54d8227393c352ee0859bc5cf01e",
    "date": "2023-12-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "5 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-10",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-11",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-2",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-3",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-4",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-5",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-6",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-7",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-8",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  },
  {
    "slug": "test2-copy-9",
    "title": "your article title",
    "excerpt": "The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in...",
    "content": "\n# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components\n\n## 1.1. **Background**\n\n### Organization Introduction\n\nThe DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.",
    "date": "2023-11-01",
    "author": "author name",
    "authorRole": "author role",
    "image": "/img/about/cti-diagram.png",
    "readTime": "1 min read",
    "category": "threat intelligence",
    "tags": [
      "tag1",
      "tag2"
    ]
  }
];
