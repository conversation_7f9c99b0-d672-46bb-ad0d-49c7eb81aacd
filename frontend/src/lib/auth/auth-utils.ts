/**
 * Authentication Utility Functions
 * 
 * This module provides utility functions for authentication operations
 * including sign in, sign out, and user management.
 */

import { signOut, getCurrentUser } from "aws-amplify/auth";
import { TokenManager, type UserInfo } from "./token-manager";

/**
 * Authentication error types
 */
export enum AuthErrorType {
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface AuthError {
  type: AuthErrorType;
  message: string;
  originalError?: Error | unknown;
}

/**
 * Create standardized auth error
 */
export function createAuthError(
  type: AuthErrorType,
  message: string,
  originalError?: Error | unknown
): AuthError {
  return {
    type,
    message,
    originalError,
  };
}

/**
 * Redirect to AWS Cognito hosted UI for sign in
 */
export async function redirectToSignIn(): Promise<void> {
  try {
    console.log("🔄 Redirecting to sign in...");

    // Get configuration from environment variables
    const cognitoDomain = process.env.NEXT_PUBLIC_COGNITO_DOMAIN;
    const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN;

    if (!cognitoDomain || !clientId || !redirectUri) {
      throw new Error("Missing Cognito configuration");
    }

    // Generate state parameter for security
    const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    // Build Cognito hosted UI URL directly
    const cognitoUrl = new URL(`https://${cognitoDomain}/oauth2/authorize`);
    cognitoUrl.searchParams.set('response_type', 'code');
    cognitoUrl.searchParams.set('client_id', clientId);
    cognitoUrl.searchParams.set('redirect_uri', redirectUri);
    cognitoUrl.searchParams.set('scope', 'openid email profile');
    cognitoUrl.searchParams.set('state', state);

    // Store state in localStorage for verification
    localStorage.setItem('cognito_auth_state', state);

    console.log("🔄 Redirecting to Cognito URL:", cognitoUrl.toString());

    // Redirect to Cognito hosted UI
    window.location.href = cognitoUrl.toString();
  } catch (error) {
    console.error("❌ Sign in redirect failed:", error);
    throw createAuthError(
      AuthErrorType.CONFIGURATION_ERROR,
      "Failed to redirect to sign in page. Please check your AWS Cognito configuration.",
      error
    );
  }
}

/**
 * Redirect to AWS Cognito hosted UI for sign up
 */
export async function redirectToSignUp(): Promise<void> {
  try {
    console.log("🔄 Redirecting to sign up...");

    // Get configuration from environment variables
    const cognitoDomain = process.env.NEXT_PUBLIC_COGNITO_DOMAIN;
    const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN;

    if (!cognitoDomain || !clientId || !redirectUri) {
      throw new Error("Missing Cognito configuration");
    }

    // Generate state parameter for security
    const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    // Build Cognito hosted UI URL directly with signup parameter
    const cognitoUrl = new URL(`https://${cognitoDomain}/oauth2/authorize`);
    cognitoUrl.searchParams.set('response_type', 'code');
    cognitoUrl.searchParams.set('client_id', clientId);
    cognitoUrl.searchParams.set('redirect_uri', redirectUri);
    cognitoUrl.searchParams.set('scope', 'openid email profile');
    cognitoUrl.searchParams.set('state', state);
    cognitoUrl.searchParams.set('signup', 'true'); // This parameter can help show signup form first

    // Store state in localStorage for verification
    localStorage.setItem('cognito_auth_state', state);

    console.log("🔄 Redirecting to Cognito signup URL:", cognitoUrl.toString());

    // Redirect to Cognito hosted UI
    window.location.href = cognitoUrl.toString();
  } catch (error) {
    console.error("❌ Sign up redirect failed:", error);
    throw createAuthError(
      AuthErrorType.CONFIGURATION_ERROR,
      "Failed to redirect to sign up page. Please check your AWS Cognito configuration.",
      error
    );
  }
}

/**
 * Sign out user and clear all tokens
 */
export async function signOutUser(): Promise<void> {
  try {
    console.log("🔄 Signing out user...");

    // Sign out from Cognito
    await signOut();

    // Clear local tokens
    TokenManager.clearTokens();

    console.log("✅ User signed out successfully");
  } catch (error) {
    console.error("❌ Sign out failed:", error);
    
    // Even if Cognito sign out fails, clear local tokens
    TokenManager.clearTokens();
    
    throw createAuthError(
      AuthErrorType.NETWORK_ERROR,
      "Sign out encountered an issue, but local session has been cleared.",
      error
    );
  }
}

/**
 * Get current authenticated user information
 */
export async function getCurrentUserInfo(): Promise<UserInfo | null> {
  try {
    // First try to get user from stored tokens
    const storedUser = TokenManager.getStoredUserInfo();
    if (storedUser) {
      return storedUser;
    }

    // If no stored user, try to get from Cognito
    const currentUser = await getCurrentUser();
    if (currentUser) {
      // Extract user info from current user object
      const userInfo: UserInfo = {
        sub: currentUser.userId,
        email: currentUser.signInDetails?.loginId || '',
        name: currentUser.username || currentUser.signInDetails?.loginId || 'User',
      };

      return userInfo;
    }

    return null;
  } catch (error) {
    console.warn("⚠️ Failed to get current user info:", error);
    return null;
  }
}

/**
 * Check if user is currently authenticated
 */
export async function isUserAuthenticated(): Promise<boolean> {
  try {
    // Check if we have valid tokens
    if (!TokenManager.areTokensExpired()) {
      const tokens = TokenManager.getStoredTokens();
      if (tokens) {
        return true;
      }
    }

    // Try to refresh tokens
    const refreshedTokens = await TokenManager.refreshTokens();
    return refreshedTokens !== null;
  } catch (error) {
    console.warn("⚠️ Authentication check failed:", error);
    return false;
  }
}

/**
 * Format user display name
 */
export function formatUserDisplayName(user: UserInfo | null): string {
  if (!user) return 'User';
  
  if (user.name && user.name !== user.email) {
    return user.name;
  }
  
  if (user.email) {
    // Extract name from email if no display name
    const emailName = user.email.split('@')[0];
    return emailName.charAt(0).toUpperCase() + emailName.slice(1);
  }
  
  return 'User';
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Get authentication status with detailed information
 */
export async function getAuthStatus(): Promise<{
  isAuthenticated: boolean;
  user: UserInfo | null;
  tokensValid: boolean;
  error?: AuthError;
}> {
  try {
    const tokensValid = !TokenManager.areTokensExpired();
    const user = await getCurrentUserInfo();
    const isAuthenticated = await isUserAuthenticated();

    return {
      isAuthenticated,
      user,
      tokensValid,
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null,
      tokensValid: false,
      error: createAuthError(
        AuthErrorType.UNKNOWN_ERROR,
        "Failed to determine authentication status",
        error
      ),
    };
  }
}

/**
 * Handle authentication callback after OAuth redirect
 */
export async function handleAuthCallback(): Promise<{
  success: boolean;
  user?: UserInfo;
  error?: AuthError;
}> {
  try {
    console.log("🔄 Handling authentication callback...");

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('error_description');

    // Check for OAuth errors
    if (error) {
      throw new Error(`OAuth error: ${error} - ${errorDescription || 'Unknown error'}`);
    }

    // Verify state parameter
    const storedState = localStorage.getItem('cognito_auth_state');
    if (state !== storedState) {
      throw new Error('Invalid state parameter - possible CSRF attack');
    }

    // Clean up stored state
    localStorage.removeItem('cognito_auth_state');

    if (!code) {
      throw new Error("No authorization code received");
    }

    // Exchange authorization code for tokens
    const tokens = await exchangeCodeForTokens(code);

    if (!tokens) {
      throw new Error("Failed to exchange code for tokens");
    }

    // Store tokens
    TokenManager.storeTokens(tokens);

    // Get user information from ID token
    const user = TokenManager.getStoredUserInfo();

    if (!user) {
      throw new Error("Failed to retrieve user information from tokens");
    }

    console.log("✅ Authentication callback handled successfully");

    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error("❌ Authentication callback failed:", error);

    // Clear any partial tokens
    TokenManager.clearTokens();

    return {
      success: false,
      error: createAuthError(
        AuthErrorType.UNAUTHORIZED,
        "Authentication failed. Please try signing in again.",
        error
      ),
    };
  }
}

/**
 * Exchange authorization code for tokens
 */
async function exchangeCodeForTokens(code: string): Promise<{
  accessToken: string;
  idToken: string;
  refreshToken: string;
  expiresAt: number;
} | null> {
  try {
    const cognitoDomain = process.env.NEXT_PUBLIC_COGNITO_DOMAIN;
    const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN;
    const clientSecret = process.env.NEXT_PUBLIC_COGNITO_CLIENT_SECRET;

    if (!cognitoDomain || !clientId || !redirectUri) {
      throw new Error("Missing Cognito configuration");
    }

    const tokenUrl = `https://${cognitoDomain}/oauth2/token`;

    const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${basicAuth}`,
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: clientId,
        code: code,
        redirect_uri: redirectUri,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
    }

    const tokenData = await response.json();

    return {
      accessToken: tokenData.access_token,
      idToken: tokenData.id_token,
      refreshToken: tokenData.refresh_token,
      expiresAt: Date.now() + (tokenData.expires_in * 1000),
    };
  } catch (error) {
    console.error("❌ Token exchange failed:", error);
    return null;
  }
}
